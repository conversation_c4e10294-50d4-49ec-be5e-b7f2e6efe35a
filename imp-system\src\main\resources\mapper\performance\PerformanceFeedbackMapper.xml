<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMapper">

    <sql id="PerformanceFeedbackVoColumns">
        f.id,
        f.main_feedback_id,
        f.group_id,
        f.group_name,
        m.feedback_code,
        m.feedback_time,
        f.primary_indicator,
        f.secondary_indicator,
        f.event_title,
        f.event_detail,
        m.event_start_time,
        m.event_end_time,
        f.recommended_level,
        f.recommended_reason,
        m.data_source,
        f.nick_name,
        f.year,
        f.month,
        f.person_type,
        m.submit_time,
        m.submitter,
        m.project_manager_audit_status,
        m.project_manager_auditor,
        m.project_manager_audit_time,
        m.project_manager_remark,
        m.final_audit,
        m.final_audit_time,
        m.final_remark
    </sql>

    <sql id="PerformanceFeedbackVoFromJoin">
        FROM tb_performance_feedback f
        LEFT JOIN tb_performance_feedback_main m ON f.main_feedback_id = m.id
    </sql>

    <sql id="PerformanceFeedbackVoWhere">
        <where>
            <if test="bo.id != null">AND f.id = #{bo.id}</if>
            <if test="bo.mainFeedbackId != null">AND f.main_feedback_id = #{bo.mainFeedbackId}</if>
            <if test="bo.groupId != null">AND f.group_id = #{bo.groupId}</if>
            <if test="bo.groupName != null and bo.groupName != ''">AND f.group_name LIKE CONCAT('%', #{bo.groupName},'%')</if>
            <if test="bo.primaryIndicator != null and bo.primaryIndicator != ''">AND f.primary_indicator = #{bo.primaryIndicator}</if>
            <if test="bo.secondaryIndicator != null and bo.secondaryIndicator != ''">AND f.secondary_indicator = #{bo.secondaryIndicator}</if>
            <if test="bo.eventTitle != null and bo.eventTitle != ''">AND f.event_title LIKE CONCAT('%',#{bo.eventTitle}, '%')</if>
            <if test="bo.eventDetail != null and bo.eventDetail != ''">AND f.event_detail LIKE CONCAT('%',
                #{bo.eventDetail}, '%')
            </if>
            <if test="bo.recommendedLevel != null and bo.recommendedLevel != ''">AND f.recommended_level =
                #{bo.recommendedLevel}
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">AND f.nick_name = #{bo.nickName}</if>
            <if test="bo.year != null">AND f.year = #{bo.year}</if>
            <if test="bo.month != null and bo.month != -1">AND f.month = #{bo.month}</if>
            <if test="bo.personType != null and bo.personType != 5 ">AND f.person_type = #{bo.personType}</if>
            <if test="bo.personType == 5">AND f.person_type IN ('3', '4', '7')</if>
            <if test="bo.submitter != null and bo.submitter != ''">AND m.submitter = #{bo.submitter}</if>
            <if test="bo.projectManagerAuditStatus != null and bo.projectManagerAuditStatus != ''">AND
                m.project_manager_audit_status = #{bo.projectManagerAuditStatus}
            </if>
            <if test="bo.finalAudit != null and bo.finalAudit != ''">AND m.final_audit = #{bo.finalAudit}
            </if>
            <if test="bo.eventStartTime != null">AND m.event_start_time &gt;= #{bo.eventStartTime}
            </if>
            <if test="bo.eventEndTime != null">AND m.event_end_time &lt;= #{bo.eventEndTime}
            </if>
            <if test="bo.dataSource != null">AND m.data_source = #{bo.dataSource}
            </if>
            <if test="bo.submitTimeBegin != null">AND m.submit_time &gt;= #{bo.submitTimeBegin}
            </if>
            <if test="bo.submitTimeEnd != null">AND m.submit_time &lt;= #{bo.submitTimeEnd}
            </if>
            <if test="bo.projectManagerAuditTimeBegin != null">AND m.project_manager_audit_time &gt;=
                #{bo.projectManagerAuditTimeBegin}
            </if>
            <if test="bo.projectManagerAuditTimeEnd != null">AND m.project_manager_audit_time &lt;=
                #{bo.projectManagerAuditTimeEnd}
            </if>
            <if test="bo.finalAuditTimeBegin != null">AND m.final_audit_time &gt;= #{bo.finalAuditTimeBegin}
            </if>
            <if test="bo.finalAuditTimeEnd != null">AND m.final_audit_time &lt;= #{bo.finalAuditTimeEnd}
            </if>
            <if test="bo.feedbackTimeBegin != null">AND m.feedback_time &gt;= #{bo.feedbackTimeBegin}
            </if>
            <if test="bo.feedbackTimeEnd != null">AND m.feedback_time &lt;= #{bo.feedbackTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 连表查询绩效反馈明细及主表信息 -->
    <select id="selectVoJoinMainList" resultType="com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo">
        SELECT
        <include refid="PerformanceFeedbackVoColumns"/>
        <include refid="PerformanceFeedbackVoFromJoin"/>
        <include refid="PerformanceFeedbackVoWhere"/>
    </select>

    <!-- 连表分页查询绩效反馈明细及主表信息 -->
    <select id="selectVoJoinMainPage" resultType="com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo">
        SELECT
        <include refid="PerformanceFeedbackVoColumns"/>
        <include refid="PerformanceFeedbackVoFromJoin"/>
        <include refid="PerformanceFeedbackVoWhere"/>
    </select>

    <delete id="deleteByMainFeedbackIds">
        DELETE FROM tb_performance_feedback
        WHERE main_feedback_id IN
        <foreach collection="mainFeedbackIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 连表查询绩效反馈明细及主表信息 -->
    <select id="selectVoApprovalJoinMainList" resultType="com.qmqb.imp.system.domain.performance.PerformanceFeedback">
        SELECT
        <include refid="PerformanceFeedbackVoColumns"/>
        <include refid="PerformanceFeedbackVoFromJoin"/>
        <where>
            <if test="bo.id != null">AND f.id = #{bo.id}</if>
            <if test="bo.mainFeedbackId != null">AND f.main_feedback_id = #{bo.mainFeedbackId}</if>
            <if test="bo.groupId != null">AND f.group_id = #{bo.groupId}</if>
            <if test="bo.groupName != null and bo.groupName != ''">AND f.group_name LIKE CONCAT('%', #{bo.groupName},
                '%')
            </if>
            <if test="bo.primaryIndicator != null and bo.primaryIndicator != ''">AND f.primary_indicator LIKE
                CONCAT('%', #{bo.primaryIndicator}, '%')
            </if>
            <if test="bo.secondaryIndicator != null and bo.secondaryIndicator != ''">AND f.secondary_indicator LIKE
                CONCAT('%', #{bo.secondaryIndicator}, '%')
            </if>
            <if test="bo.eventTitle != null and bo.eventTitle != ''">AND f.event_title LIKE CONCAT('%',
                #{bo.eventTitle}, '%')
            </if>
            <if test="bo.eventDetail != null and bo.eventDetail != ''">AND f.event_detail LIKE CONCAT('%',
                #{bo.eventDetail}, '%')
            </if>
            <if test="bo.recommendedLevel != null and bo.recommendedLevel != ''">AND f.recommended_level =
                #{bo.recommendedLevel}
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">AND f.nick_name = #{bo.nickName}</if>
            <if test="bo.year != null">AND f.year = #{bo.year}</if>
            <if test="bo.month != null">AND f.month = #{bo.month}</if>
            and m.final_audit = 'APPROVED'
        </where>
    </select>

</mapper>
