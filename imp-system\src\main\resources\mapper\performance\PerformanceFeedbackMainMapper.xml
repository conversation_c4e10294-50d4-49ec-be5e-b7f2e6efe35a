<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper">

    <resultMap type="com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain" id="PerformanceFeedbackMainResult">
        <result property="id" column="id"/>
        <result property="feedbackCode" column="feedback_code"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="feedbackTime" column="feedback_time"/>
        <result property="primaryIndicator" column="primary_indicator"/>
        <result property="secondaryIndicator" column="secondary_indicator"/>
        <result property="eventTitle" column="event_title"/>
        <result property="eventDetail" column="event_detail"/>
        <result property="eventStartTime" column="event_start_time"/>
        <result property="eventEndTime" column="event_end_time"/>
        <result property="dataSource" column="data_source"/>
        <result property="submitStatus" column="submit_status"/>
        <result property="submitTime" column="submit_time"/>
        <result property="submitter" column="submitter"/>
        <result property="projectManagerAuditStatus" column="project_manager_audit_status"/>
        <result property="projectManagerAuditor" column="project_manager_auditor"/>
        <result property="projectManagerAuditTime" column="project_manager_audit_time"/>
        <result property="projectManagerRemark" column="project_manager_remark"/>
        <result property="finalAudit" column="final_audit"/>
        <result property="finalAuditTime" column="final_audit_time"/>
        <result property="finalRemark" column="final_remark"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <select id="getMaxMainFeedbackCodeByDatePrefix" resultType="java.lang.String">
        select feedback_code
        from tb_performance_feedback_main
        where feedback_code like concat(#{datePrefix}, '%')
        order by feedback_code desc limit 1;
    </select>

    <select id="getIdByNickNameAndYearMonth" resultType="java.lang.String">
        SELECT DISTINCT m.id
        FROM tb_performance_feedback_main m
                 JOIN tb_performance_feedback f ON m.id = f.main_feedback_id
        WHERE m.data_source = 'SYSTEM_GENERATED'
          AND f.nick_name = #{nickName}
          AND f.year = #{year}
          AND f.month = #{month}
        order by m.id
        for update;
    </select>

    <!-- 根据二类指标查询绩效反馈记录 -->
    <select id="getBySecondaryIndicator" resultType="com.qmqb.imp.system.domain.performance.PerformanceFeedback">
        SELECT
            f.id,
            f.main_feedback_id,
            f.group_id,
            f.group_name,
            f.primary_indicator,
            f.secondary_indicator,
            f.event_title,
            f.event_detail,
            f.recommended_level,
            f.recommended_reason,
            f.nick_name,
            f.year,
            f.month,
            f.person_type,
            f.create_by,
            f.create_time,
            f.update_by,
            f.update_time
        FROM tb_performance_feedback_main m
        JOIN tb_performance_feedback f ON m.id = f.main_feedback_id
        WHERE m.secondary_indicator = #{secondaryIndicator}
          AND f.year = #{year}
          AND f.month = #{month}
          <if test="nickNames != null and nickNames.size() > 0">
              AND f.nick_name IN
              <foreach collection="nickNames" item="nickName" open="(" separator="," close=")">
                  #{nickName}
              </foreach>
          </if>
    </select>

    <!-- 分页查询主表全部数据，支持反馈表nick_name、group_id、recommended_level及主表条件 -->
    <select id="selectMainPageByFeedbackCondition" resultType="com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainVo">
        SELECT DISTINCT m.*
        FROM tb_performance_feedback_main m
        JOIN tb_performance_feedback f ON m.id = f.main_feedback_id
        <where>
            <if test="queryBo.nickName != null and queryBo.nickName != ''">
                AND f.nick_name LIKE CONCAT('%', #{queryBo.nickName}, '%')
            </if>
            <if test="queryBo.groupId != null">
                AND f.group_id = #{queryBo.groupId}
            </if>
            <if test="queryBo.recommendedLevel != null and queryBo.recommendedLevel != ''">
                AND f.recommended_level = #{queryBo.recommendedLevel}
            </if>
            <if test="queryBo.feedbackCode != null and queryBo.feedbackCode != ''">
                AND m.feedback_code = #{queryBo.feedbackCode}
            </if>
            <if test="queryBo.primaryIndicator != null and queryBo.primaryIndicator != ''">
                AND m.primary_indicator = #{queryBo.primaryIndicator}
            </if>
            <if test="queryBo.secondaryIndicator != null and queryBo.secondaryIndicator != ''">
                AND m.secondary_indicator = #{queryBo.secondaryIndicator}
            </if>
            <if test="queryBo.projectManagerAuditStatus != null and queryBo.projectManagerAuditStatus != ''">
                AND m.project_manager_audit_status = #{queryBo.projectManagerAuditStatus}
            </if>
            <if test="queryBo.finalAudit != null and queryBo.finalAudit != ''">
                AND m.final_audit = #{queryBo.finalAudit}
            </if>
            <if test="queryBo.dataSource != null and queryBo.dataSource != ''">
                AND m.data_source = #{queryBo.dataSource}
            </if>
            <if test="queryBo.submitStatus != null and queryBo.submitStatus != ''">
                AND m.submit_status = #{queryBo.submitStatus}
            </if>
            <if test="queryBo.feedbackTimeBegin != null">
                AND m.feedback_time &gt;= #{queryBo.feedbackTimeBegin}
            </if>
            <if test="queryBo.feedbackTimeEnd != null">
                AND m.feedback_time &lt;= #{queryBo.feedbackTimeEnd}
            </if>
            <if test="queryBo.eventStartTime != null">
                AND m.event_start_time &gt;= #{queryBo.eventStartTime}
            </if>
            <if test="queryBo.eventEndTime != null">
                AND m.event_end_time &lt;= #{queryBo.eventEndTime}
            </if>
            <if test="queryBo.submitTimeBegin != null">
                AND m.submit_time &gt;= #{queryBo.submitTimeBegin}
            </if>
            <if test="queryBo.submitTimeEnd != null">
                AND m.submit_time &lt;= #{queryBo.submitTimeEnd}
            </if>
            <if test="queryBo.projectManagerAuditTimeBegin != null">
                AND m.project_manager_audit_time &gt;= #{queryBo.projectManagerAuditTimeBegin}
            </if>
            <if test="queryBo.projectManagerAuditTimeEnd != null">
                AND m.project_manager_audit_time &lt;= #{queryBo.projectManagerAuditTimeEnd}
            </if>
            <if test="queryBo.finalAuditTimeBegin != null">
                AND m.final_audit_time &gt;= #{queryBo.finalAuditTimeBegin}
            </if>
            <if test="queryBo.finalAuditTimeEnd != null">
                AND m.final_audit_time &lt;= #{queryBo.finalAuditTimeEnd}
            </if>
            <!-- 权限过滤：非管理员和技术总监才加限制 -->
            <if test="queryBo.isAdmin == null or !queryBo.isAdmin">
                <if test="queryBo.isJszxAdmin == null or !queryBo.isJszxAdmin">
                    <!-- 项管权限 -->
                    <if test="queryBo.isProjectManager != null and queryBo.isProjectManager">
                        AND (
                        (m.submitter = #{queryBo.userName} OR m.create_by = #{queryBo.userName})
                        OR
                        (m.project_manager_auditor = #{queryBo.userName} and m.submit_status = 'SUBMITTED')
                        <!-- 将 prod_fault 条件移到这里面 -->
                        <if test="queryBo.secondaryIndicator == null and queryBo.primaryIndicator == null">
                            OR (m.secondary_indicator = 'prod_fault')
                            OR (m.secondary_indicator = 'dev_spec' AND m.data_source = 'SYSTEM_GENERATED')
                        </if>
                        <if test="queryBo.primaryIndicator == 'work_achievement'">
                            OR (m.secondary_indicator = 'prod_fault')
                        </if>
                        <if test="queryBo.primaryIndicator == 'work_quality'">
                            OR (m.secondary_indicator = 'dev_spec' AND m.data_source = 'SYSTEM_GENERATED')
                        </if>
                        <if test="queryBo.secondaryIndicator == 'prod_fault'">
                            OR (m.secondary_indicator = 'prod_fault')
                            OR (m.secondary_indicator = 'dev_spec' AND m.data_source = 'SYSTEM_GENERATED')
                        </if>
                        <if test="queryBo.secondaryIndicator == 'dev_spec'">
                            OR (m.secondary_indicator = 'dev_spec' AND m.data_source = 'SYSTEM_GENERATED')
                        </if>
                        )
                    </if>
                    <!-- 普通组长权限 -->
                    <if test="queryBo.isProjectManager == null or !queryBo.isProjectManager">
                        AND (
                            m.submitter = #{queryBo.userName}
                            OR
                            m.create_by = #{queryBo.userName}
                        )
                    </if>
                </if>
            </if>
        </where>
        GROUP BY m.id
    </select>

</mapper>
