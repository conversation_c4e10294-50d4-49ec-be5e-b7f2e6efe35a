package com.qmqb.imp.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.dto.BranchStatisticsDTO;
import com.qmqb.imp.common.core.domain.dto.CodeQueryDTO;
import com.qmqb.imp.common.core.domain.vo.BusinessConfigSelectVo;
import com.qmqb.imp.common.core.domain.vo.UserTeamSelectVO;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.Project;
import com.qmqb.imp.system.domain.ScanProject;
import com.qmqb.imp.system.domain.bo.BusinessConfigBo;
import com.qmqb.imp.system.domain.bo.ProjectBo;
import com.qmqb.imp.system.domain.vo.ProjectVo;
import com.qmqb.imp.system.mapper.BranchMapper;
import com.qmqb.imp.system.mapper.ProjectMapper;
import com.qmqb.imp.system.mapper.ScanProjectMapper;
import com.qmqb.imp.system.service.IBusinessConfigService;
import com.qmqb.imp.system.service.IProjectService;
import com.qmqb.imp.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 项目管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@RequiredArgsConstructor
@Service
public class ProjectServiceImpl implements IProjectService {


    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private BranchMapper branchMapper;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private IBusinessConfigService iBusinessConfigService;

    @Autowired
    private ScanProjectMapper scanProjectMapper;

    @Override
    public Page<ProjectVo> page(ProjectBo request) {
        Page<ProjectVo> page = new Page<>(request.getPageNum(),request.getPageSize());
        if (StrUtil.isNotBlank(request.getOrderByField())) {
            request.setOrderByField(StringUtils.toUnderScoreCase(request.getOrderByField()));
        }
        List<ProjectVo> projectVos = this.projectMapper.getLists(page, request);
        page.setRecords(projectVos);
        return page;
    }

    @Override
    public ProjectVo getByProjectId(Long pId) {
       return this.projectMapper.getByProjectId(pId);
    }

    @Override
    public int insert(Project project) {
        return this.projectMapper.insert(project);
    }

    @Override
    public List<ProjectVo> getByProjects() {
        return this.projectMapper.getByProjects();
    }

    @Override
    public int batchsByProjectIds(List<Long> ids) {
        return this.projectMapper.deleteBatchIds(ids);
    }

    @Override
    public Long selectProjectsCountByCreateTime(Date beginTime, Date endTime) {
        return this.projectMapper.selectCount(Wrappers.lambdaQuery(Project.class).between(Project::getPCreatetime, beginTime, endTime));
    }

    @Override
    public Boolean updateBatchById(List<Project> pros) {
        return this.projectMapper.updateBatchById(pros);
    }

    @Override
    public Boolean insertBatch(List<Project> pros) {
        return this.projectMapper.insertBatch(pros);
    }

    @Override
    public Boolean edit(ProjectBo request) {
        if (request==null && request.getPId()==null){
            throw new ServiceException("项目ID不能为空");
        }
        ProjectVo byProjectId = projectMapper.selectVoById(request.getPId());
        if (byProjectId==null){
            throw new ServiceException("项目不存在");
        }
        String pDevDept = request.getPDevDept();
        String pTestDept = request.getPTestDept();
        Long pBroadBusiness = request.getPBroadBusiness();
        Long pNarrowBusiness = request.getPNarrowBusiness();
        //部门
        Map<Long,String> userTeamSelectVoS = iSysDeptService.selectDeptListByType(null).stream().collect(Collectors.toMap(UserTeamSelectVO::getValue, UserTeamSelectVO::getDesc));
        Boolean isDevConfigMissing  = (pDevDept!=null && userTeamSelectVoS.get(Long.valueOf(pDevDept))==null) || (pTestDept!=null && userTeamSelectVoS.get(Long.valueOf(pTestDept))==null);
        if (isDevConfigMissing){
            throw new ServiceException("部门配置不存在");
        }
        //业务
        Map<Long,String> businessConfigSelectVos = iBusinessConfigService.listByType(new BusinessConfigBo()).stream().collect(Collectors.toMap(BusinessConfigSelectVo::getId, BusinessConfigSelectVo::getName));
        Boolean isBusinessConfigMissing =(pBroadBusiness!=null && businessConfigSelectVos.get(pBroadBusiness)==null) || (pNarrowBusiness!=null && businessConfigSelectVos.get(pNarrowBusiness)==null);
        if (isBusinessConfigMissing ){
            throw new ServiceException("业务配置不存在");
        }
        UpdateWrapper<Project> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("p_id",request.getPId());
        updateWrapper.set("p_dev_dept",request.getPDevDept());
        updateWrapper.set("p_test_dept",request.getPTestDept());
        updateWrapper.set("p_broad_business",request.getPBroadBusiness());
        updateWrapper.set("p_narrow_business",request.getPNarrowBusiness());
        this.projectMapper.update(null,updateWrapper);
        //修改代码扫描记录表
        UpdateWrapper<ScanProject> updateWrapper1 = new UpdateWrapper<>();
        updateWrapper1.eq("p_id",request.getPId());
        updateWrapper1.eq("last_scan_flag", CommConstants.CommonVal.ONE);
        updateWrapper1.eq("del_flag",CommConstants.CommonVal.ZERO);
        updateWrapper1.set("dev_dept",request.getPDevDept());
        this.scanProjectMapper.update(null,updateWrapper1);
        return true;
    }

    @Override
    public List<Project> getByProjects(CodeQueryDTO request) {
        boolean flag=((request.getPDevDept()!=null && !request.getPDevDept().isEmpty()) || (request.getPTestDept()!=null && !request.getPTestDept().isEmpty()) || request.getPBroadBusiness()!=null || request.getPNarrowBusiness()!=null);
        if (flag){
            QueryWrapper<Project> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(request.getPDevDept()!=null && !request.getPDevDept().isEmpty(),"p_dev_dept",request.getPDevDept());
            queryWrapper.eq(request.getPTestDept()!=null && !request.getPTestDept().isEmpty(),"p_test_dept",request.getPTestDept());
            queryWrapper.eq(request.getPBroadBusiness()!=null,"p_broad_business",request.getPBroadBusiness());
            queryWrapper.eq(request.getPNarrowBusiness()!=null,"p_narrow_business",request.getPNarrowBusiness());
            return projectMapper.selectList(queryWrapper);
        }
        return new ArrayList<>();
    }

    @Override
    public List<BranchStatisticsDTO> branchStatisticsList(Integer number) {
        return projectMapper.branchStatisticsList(number);
    }

    @Override
    public List<Project> selectProjectByIds(List<Long> pids) {
        return projectMapper.selectBatchIds(pids);
    }
}
