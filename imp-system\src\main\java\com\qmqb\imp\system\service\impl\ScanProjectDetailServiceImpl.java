package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.ScanProjectDetail;
import com.qmqb.imp.system.domain.bo.ScanProjectDetailBo;
import com.qmqb.imp.system.domain.vo.ScanFileGroupInfoVo;
import com.qmqb.imp.system.domain.vo.ScanProjectDetailVo;
import com.qmqb.imp.system.mapper.ScanProjectDetailMapper;
import com.qmqb.imp.system.service.IScanProjectDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 扫描项目记录详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@RequiredArgsConstructor
@Service
public class ScanProjectDetailServiceImpl implements IScanProjectDetailService {

    private final ScanProjectDetailMapper baseMapper;

    /**
     * 查询扫描项目记录详情
     */
    @Override
    public ScanProjectDetailVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询扫描项目记录详情列表
     */
    @Override
    public TableDataInfo<ScanProjectDetailVo> queryPageList(ScanProjectDetailBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn(bo.getOrderByField());
        }
        if (StringUtils.isBlank(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc(bo.getOrderRule());
        }
        LambdaQueryWrapper<ScanProjectDetail> lqw = buildQueryWrapper(bo);
        Page<ScanProjectDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询扫描项目记录详情列表
     */
    @Override
    public List<ScanProjectDetailVo> queryList(ScanProjectDetailBo bo) {
        LambdaQueryWrapper<ScanProjectDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ScanProjectDetail> buildQueryWrapper(ScanProjectDetailBo bo) {
        LambdaQueryWrapper<ScanProjectDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPId() != null, ScanProjectDetail::getPId, bo.getPId());
        lqw.eq(ScanProjectDetail::getIsHandle, CommConstants.CommonVal.ZERO);
        lqw.eq(StringUtils.isNotBlank(bo.getScanFileUrl()), ScanProjectDetail::getScanFileUrl, bo.getScanFileUrl());
        lqw.eq(bo.getScanBeginLine() != null, ScanProjectDetail::getScanBeginLine, bo.getScanBeginLine());
        lqw.eq(bo.getScanEndLine() != null, ScanProjectDetail::getScanEndLine, bo.getScanEndLine());
        lqw.eq(bo.getScanBeginColumn() != null, ScanProjectDetail::getScanBeginColumn, bo.getScanBeginColumn());
        lqw.eq(bo.getScanEndColumn() != null, ScanProjectDetail::getScanEndColumn, bo.getScanEndColumn());
        lqw.eq(bo.getScanVersion() != null, ScanProjectDetail::getScanVersion, bo.getScanVersion());
        lqw.eq(bo.getFileId() != null, ScanProjectDetail::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getScanRule()), ScanProjectDetail::getScanRule, bo.getScanRule());
        lqw.eq(StringUtils.isNotBlank(bo.getScanRuleSet()), ScanProjectDetail::getScanRuleSet, bo.getScanRuleSet());
        lqw.eq(StringUtils.isNotBlank(bo.getScanClass()), ScanProjectDetail::getScanClass, bo.getScanClass());
        lqw.eq(bo.getScanPriority() != null, ScanProjectDetail::getScanPriority, bo.getScanPriority());
        lqw.eq(StringUtils.isNotBlank(bo.getScanMethod()), ScanProjectDetail::getScanMethod, bo.getScanMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getScanVariable()), ScanProjectDetail::getScanVariable, bo.getScanVariable());
        lqw.eq(StringUtils.isNotBlank(bo.getScanPackage()), ScanProjectDetail::getScanPackage, bo.getScanPackage());
        lqw.eq(StringUtils.isNotBlank(bo.getScanDescribe()), ScanProjectDetail::getScanDescribe, bo.getScanDescribe());
        return lqw;
    }

    /**
     * 新增扫描项目记录详情
     */
    @Override
    public Boolean insertByBo(ScanProjectDetailBo bo) {
        ScanProjectDetail add = BeanUtil.toBean(bo, ScanProjectDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改扫描项目记录详情
     */
    @Override
    public Boolean updateByBo(ScanProjectDetailBo bo) {
        ScanProjectDetail update = BeanUtil.toBean(bo, ScanProjectDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ScanProjectDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除扫描项目记录详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量删除扫描项目记录详情根据主键
     * @param deleteIds
     */
    @Override
    public Boolean batchDeleteByPids(Set<Long> deleteIds) {
        LambdaQueryWrapper<ScanProjectDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ScanProjectDetail::getPId, deleteIds);
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    public Boolean insertBatch(List<ScanProjectDetail> scanProjectDetailList) {
        return baseMapper.insertBatch(scanProjectDetailList);
    }

    @Override
    public Long selectAmountByPriority(Long pid, int priority) {
        return baseMapper.selectAmountByPriority(pid,priority);
    }

    @Override
    public int updatePreviousScanToOld(List<String> scanFileUrls, Long pid) {
        return baseMapper.updatePreviousScanToOld(scanFileUrls,pid);
    }

    @Override
    public Boolean softDeleteByProjectId(Long projectId) {
        LambdaUpdateWrapper<ScanProjectDetail> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(ScanProjectDetail::getPId, projectId)
                    .set(ScanProjectDetail::getDelFlag, CommConstants.CommonVal.TWO);
        return baseMapper.update(null, updateWrapper) > 0;
    }
}
