<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ProjectMapper">
    <resultMap type="com.qmqb.imp.system.domain.Project" id="projectResultMap">
        <result property="pId" column="p_id"/>
        <result property="pName" column="p_name"/>
        <result property="pNamespace" column="p_namespace"/>
        <result property="pCreatetime" column="p_createtime"/>
        <result property="pLastUpdateTime" column="p_last_update_time"/>
        <result property="pDesc" column="p_desc"/>
        <result property="pWebUrl" column="p_web_url"/>
        <result property="pVisibility" column="p_visibility"/>
        <result property="pSyncDatetime" column="p_sync_datetime"/>
        <result property="pDevDept" column="p_dev_dept"/>
        <result property="pTestDept" column="p_test_dept"/>
        <result property="pBroadBusiness" column="p_broad_business"/>
        <result property="pNarrowBusiness" column="p_narrow_business"/>
        <result property="pBranchCount" column="p_branch_count"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        p_id
        , p_name, p_namespace, p_createtime, p_last_update_time, p_desc, p_web_url,
            p_visibility, p_sync_datetime,p_dev_dept,
            p_test_dept,p_broad_business, p_narrow_business, p_branch_count
    </sql>

    <select id="getLists" resultType="com.qmqb.imp.system.domain.vo.ProjectVo">
        SELECT
        <include refid="Base_Column_List"/>,sd1.dept_name p_dev_dept_name,sd2.dept_name p_test_dept_name, tbc1.config_name p_broad_business_name,tbc2.config_name p_narrow_business_name
        FROM tb_project tp
        LEFT JOIN sys_dept sd1 ON tp.p_dev_dept = sd1.dept_id
        LEFT JOIN sys_dept sd2 ON tp.p_test_dept = sd2.dept_id
        LEFT JOIN tb_business_config tbc1 ON tp.p_broad_business = tbc1.id
        LEFT JOIN tb_business_config tbc2 ON tp.p_narrow_business = tbc2.id
        <where>
            <if test="request.pName !=null and request.pName !=''">
                AND p_name LIKE CONCAT ('%',#{request.pName},'%')
            </if>

            <if test="request.pDesc !=null and request.pDesc !=''">
                AND p_desc LIKE CONCAT ('%',#{request.pDesc},'%')
            </if>

            <if test="request.pNamespace !=null and request.pNamespace !=''">
                AND p_namespace LIKE CONCAT ('%',#{request.pNamespace},'%')
            </if>

            <if test="request.pDevDept !=null and request.pDevDept != '' and request.pDevDept != 0 ">
                AND p_dev_dept =#{request.pDevDept}
            </if>

            <if test="request.pTestDept !=null and request.pTestDept != '' and request.pTestDept != 0 ">
                AND p_test_dept =#{request.pTestDept}
            </if>

            <if test="request.pBroadBusiness !=null  and request.pBroadBusiness != 0">
                AND p_broad_business =#{request.pBroadBusiness}
            </if>

            <if test="request.pNarrowBusiness !=null  and request.pNarrowBusiness != 0">
                AND p_narrow_business =#{request.pNarrowBusiness}
            </if>
        </where>
        <choose>
            <when test="request.orderByField !=null and request.orderByField !='' and request.orderRule != '' and request.orderRule != null">
                <choose>
                    <when test="request.orderByField == 'p_broad_business_name'">
                        ORDER BY tbc1.pinyin_name ${request.orderRule}
                    </when>
                    <when test="request.orderByField == 'p_narrow_business_name'">
                        ORDER BY tbc2.pinyin_name ${request.orderRule}
                    </when>
                    <otherwise>
                        ORDER BY ${request.orderByField} ${request.orderRule}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by p_createtime DESC
            </otherwise>
        </choose>
    </select>

    <select id="getByProjectId" resultType="com.qmqb.imp.system.domain.vo.ProjectVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_project
        WHERE p_id = #{pId}
    </select>

    <select id="getByProjects" resultType="com.qmqb.imp.system.domain.vo.ProjectVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_project
    </select>
    <select id="branchStatisticsList" resultType="com.qmqb.imp.common.core.domain.dto.BranchStatisticsDTO">
        SELECT
        IFNULL(tp.p_id,'') pId,
        IFNULL(tp.p_name,'') pName,
        IFNULL(tp.p_dev_dept,'') pDevDept,
        IFNULL(tp.p_branch_count,'') pBranchCount,
        IFNULL(sd.dept_name,'') pDevDeptName,
        IF(tp.p_dev_dept is NULL,'',IFNULL(su.num,'0')) pDevDeptPeopleNumber
        FROM tb_project tp
        LEFT JOIN sys_dept sd ON tp.p_dev_dept = sd.dept_id
        LEFT JOIN (select dept_id, count(1) num from sys_user group by dept_id) su
        ON tp.p_dev_dept = su.dept_id
        <where>
            <if test="number != null">
                AND tp.p_branch_count > #{number}
            </if>
        </where>
    </select>
</mapper>
