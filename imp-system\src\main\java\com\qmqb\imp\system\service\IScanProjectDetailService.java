package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.ScanProjectDetail;
import com.qmqb.imp.system.domain.bo.ScanProjectDetailBo;
import com.qmqb.imp.system.domain.vo.ScanProjectDetailVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 扫描项目记录详情Service接口
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IScanProjectDetailService {

    /**
     * 查询扫描项目记录详情
     * @param id
     * @return
     */
    ScanProjectDetailVo queryById(Long id);

    /**
     * 查询扫描项目记录详情列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ScanProjectDetailVo> queryPageList(ScanProjectDetailBo bo, PageQuery pageQuery);

    /**
     * 查询扫描项目记录详情列表
     * @param bo
     * @return
     */
    List<ScanProjectDetailVo> queryList(ScanProjectDetailBo bo);

    /**
     * 新增扫描项目记录详情
     * @param bo
     * @return
     */
    Boolean insertByBo(ScanProjectDetailBo bo);

    /**
     * 修改扫描项目记录详情
     * @param bo
     * @return
     */
    Boolean updateByBo(ScanProjectDetailBo bo);

    /**
     * 校验并批量删除扫描项目记录详情信息
     * @param ids
     * @param isValid
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 通过项目id批量删除项目
     * @param deleteIds
     * @return
     */
    Boolean batchDeleteByPids(Set<Long> deleteIds);

    /**
     * 批量插入
     * @param scanProjectDetailList
     * @return
     */
    Boolean insertBatch(List<ScanProjectDetail> scanProjectDetailList);

    /**
     * 查询项目问题数
     * @param id
     * @param priority
     * @return
     */
    Long selectAmountByPriority(Long id, int priority);

    /**
     * 修改旧的扫描数据
     * @param scanFileUrls
     * @param id
     * @return
     */
    int updatePreviousScanToOld(List<String> scanFileUrls, Long id);

    /**
     * 根据项目ID软删除扫描项目详情记录
     *
     * @param projectId 项目ID
     * @return 是否成功
     */
    Boolean softDeleteByProjectId(Long projectId);

    /**
     * 批量软删除扫描项目详情记录
     *
     * @param projectIds 项目ID集合
     * @return 是否成功
     */
    Boolean batchSoftDeleteByProjectIds(Set<Long> projectIds);
}
